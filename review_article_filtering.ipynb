{"cells": [{"cell_type": "code", "execution_count": 1, "id": "e81e5e90", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "df = pd.read_parquet('/home/<USER>/top_articles.parquet')\n"]}, {"cell_type": "code", "execution_count": 3, "id": "aa046094", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>paper_id</th>\n", "      <th>ans_endpoints_available</th>\n", "      <th>ans_metadata_available</th>\n", "      <th>ans_endpoints_count</th>\n", "      <th>ans_metadata_count</th>\n", "      <th>oa_works_id_x</th>\n", "      <th>fwci</th>\n", "      <th>type</th>\n", "      <th>oa_works_id_y</th>\n", "      <th>rawContent</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>W1491690127</td>\n", "      <td>[Antigen Expression (Biomarker High or Low Mod...</td>\n", "      <td>[Linker Type, Linker Name, Payload Name, SS Co...</td>\n", "      <td>5</td>\n", "      <td>10</td>\n", "      <td>W1491690127</td>\n", "      <td>3.117</td>\n", "      <td>article</td>\n", "      <td>W1491690127</td>\n", "      <td>&lt;article article-type=\"research-article\" xmlns...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>W1556136319</td>\n", "      <td>[Antigen Expression (Biomarker High or Low Mod...</td>\n", "      <td>[ADC Name, Antibody Name, Antigen Name, Linker...</td>\n", "      <td>10</td>\n", "      <td>10</td>\n", "      <td>W1556136319</td>\n", "      <td>2.582</td>\n", "      <td>article</td>\n", "      <td>W1556136319</td>\n", "      <td>&lt;article article-type=\"review-article\" xmlns:m...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>W1587404401</td>\n", "      <td>[Antigen Expression (Biomarker High or Low Mod...</td>\n", "      <td>[Antigen Name, Payload Name, Model Type, Cance...</td>\n", "      <td>9</td>\n", "      <td>5</td>\n", "      <td>W1587404401</td>\n", "      <td>2.783</td>\n", "      <td>article</td>\n", "      <td>W1587404401</td>\n", "      <td>&lt;article article-type=\"research-article\" xmlns...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>W1670498114</td>\n", "      <td>[Antigen Expression (Biomarker High or Low Mod...</td>\n", "      <td>[ADC Name, Antibody Name, Antigen Name, Linker...</td>\n", "      <td>6</td>\n", "      <td>14</td>\n", "      <td>W1670498114</td>\n", "      <td>8.222</td>\n", "      <td>article</td>\n", "      <td>W1670498114</td>\n", "      <td>&lt;article article-type=\"research-article\" xmlns...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>W1856926169</td>\n", "      <td>[ADC treatment concentration, BM pos cell decr...</td>\n", "      <td>[ADC Name, Antibody Name, Antigen Name, Payloa...</td>\n", "      <td>5</td>\n", "      <td>7</td>\n", "      <td>W1856926169</td>\n", "      <td>1.484</td>\n", "      <td>article</td>\n", "      <td>W1856926169</td>\n", "      <td>&lt;article article-type=\"research-article\" xmlns...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>764</th>\n", "      <td>W4406491310</td>\n", "      <td>[Antigen Expression (Biomarker High or Low Mod...</td>\n", "      <td>[ADC Name, ADC Company, Antibody Name, Antigen...</td>\n", "      <td>6</td>\n", "      <td>8</td>\n", "      <td>W4406491310</td>\n", "      <td>0.000</td>\n", "      <td>article</td>\n", "      <td>W4406491310</td>\n", "      <td>&lt;article article-type=\"research-article\" xml:l...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>765</th>\n", "      <td>W4406500393</td>\n", "      <td>[Antigen Expression (Biomarker High or Low Mod...</td>\n", "      <td>[ADC Name, Antibody Name, Antigen Name, Payloa...</td>\n", "      <td>10</td>\n", "      <td>8</td>\n", "      <td>W4406500393</td>\n", "      <td>0.000</td>\n", "      <td>article</td>\n", "      <td>W4406500393</td>\n", "      <td>&lt;article article-type=\"research-article\" xml:l...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>766</th>\n", "      <td>W4406540775</td>\n", "      <td>[Antigen Expression (Biomarker High or Low Mod...</td>\n", "      <td>[Antigen Name, Model Name, Model Type, Cancer ...</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "      <td>W4406540775</td>\n", "      <td>0.000</td>\n", "      <td>article</td>\n", "      <td>W4406540775</td>\n", "      <td>&lt;article article-type=\"research-article\" xml:l...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>767</th>\n", "      <td>W4406618624</td>\n", "      <td>[Antigen Expression (Biomarker High or Low Mod...</td>\n", "      <td>[Antibody Binding Epitope, Antibody Binding Ep...</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>W4406618624</td>\n", "      <td>0.000</td>\n", "      <td>article</td>\n", "      <td>W4406618624</td>\n", "      <td>&lt;article article-type=\"research-article\" xml:l...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>768</th>\n", "      <td>W4406848255</td>\n", "      <td>[Antigen Expression (Biomarker High or Low Mod...</td>\n", "      <td>[Antibody Name, Antigen Name, Model Type, Canc...</td>\n", "      <td>2</td>\n", "      <td>4</td>\n", "      <td>W4406848255</td>\n", "      <td>0.000</td>\n", "      <td>article</td>\n", "      <td>W4406848255</td>\n", "      <td>&lt;article dtd-version=\"1.3\" article-type=\"resea...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>571 rows × 10 columns</p>\n", "</div>"], "text/plain": ["        paper_id                            ans_endpoints_available  \\\n", "0    W1491690127  [Antigen Expression (Biomarker High or Low Mod...   \n", "1    W1556136319  [Antigen Expression (Biomarker High or Low Mod...   \n", "2    W1587404401  [Antigen Expression (Biomarker High or Low Mod...   \n", "3    W1670498114  [Antigen Expression (Biomarker High or Low Mod...   \n", "4    W1856926169  [ADC treatment concentration, BM pos cell decr...   \n", "..           ...                                                ...   \n", "764  W4406491310  [Antigen Expression (Biomarker High or Low Mod...   \n", "765  W4406500393  [Antigen Expression (Biomarker High or Low Mod...   \n", "766  W4406540775  [Antigen Expression (Biomarker High or Low Mod...   \n", "767  W4406618624  [Antigen Expression (Biomarker High or Low Mod...   \n", "768  W4406848255  [Antigen Expression (Biomarker High or Low Mod...   \n", "\n", "                                ans_metadata_available  ans_endpoints_count  \\\n", "0    [Linker Type, Linker Name, Payload Name, SS Co...                    5   \n", "1    [ADC Name, Antibody Name, Antigen Name, Linker...                   10   \n", "2    [Antigen Name, Payload Name, Model Type, Cance...                    9   \n", "3    [ADC Name, Antibody Name, Antigen Name, Linker...                    6   \n", "4    [ADC Name, Antibody Name, Antigen Name, Payloa...                    5   \n", "..                                                 ...                  ...   \n", "764  [ADC Name, ADC Company, Antibody Name, Antigen...                    6   \n", "765  [ADC Name, Antibody Name, Antigen Name, Payloa...                   10   \n", "766  [Antigen Name, Model Name, Model Type, Cancer ...                    8   \n", "767  [Antibody Binding Epitope, Antibody Binding Ep...                    1   \n", "768  [Antibody Name, Antigen Name, Model Type, Canc...                    2   \n", "\n", "     ans_metadata_count oa_works_id_x   fwci     type oa_works_id_y  \\\n", "0                    10   W1491690127  3.117  article   W1491690127   \n", "1                    10   W1556136319  2.582  article   W1556136319   \n", "2                     5   W1587404401  2.783  article   W1587404401   \n", "3                    14   W1670498114  8.222  article   W1670498114   \n", "4                     7   W1856926169  1.484  article   W1856926169   \n", "..                  ...           ...    ...      ...           ...   \n", "764                   8   W4406491310  0.000  article   W4406491310   \n", "765                   8   W4406500393  0.000  article   W4406500393   \n", "766                   4   W4406540775  0.000  article   W4406540775   \n", "767                   5   W4406618624  0.000  article   W4406618624   \n", "768                   4   W4406848255  0.000  article   W4406848255   \n", "\n", "                                            rawContent  \n", "0    <article article-type=\"research-article\" xmlns...  \n", "1    <article article-type=\"review-article\" xmlns:m...  \n", "2    <article article-type=\"research-article\" xmlns...  \n", "3    <article article-type=\"research-article\" xmlns...  \n", "4    <article article-type=\"research-article\" xmlns...  \n", "..                                                 ...  \n", "764  <article article-type=\"research-article\" xml:l...  \n", "765  <article article-type=\"research-article\" xml:l...  \n", "766  <article article-type=\"research-article\" xml:l...  \n", "767  <article article-type=\"research-article\" xml:l...  \n", "768  <article dtd-version=\"1.3\" article-type=\"resea...  \n", "\n", "[571 rows x 10 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 4, "id": "aae3b6b4", "metadata": {}, "outputs": [], "source": ["articles_sorted = df.sort_values('fwci',ascending=False).head(150)"]}, {"cell_type": "code", "execution_count": 5, "id": "7b934b66", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>paper_id</th>\n", "      <th>ans_endpoints_available</th>\n", "      <th>ans_metadata_available</th>\n", "      <th>ans_endpoints_count</th>\n", "      <th>ans_metadata_count</th>\n", "      <th>oa_works_id_x</th>\n", "      <th>fwci</th>\n", "      <th>type</th>\n", "      <th>oa_works_id_y</th>\n", "      <th>rawContent</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>742</th>\n", "      <td>W4402167769</td>\n", "      <td>[Antigen Expression (Biomarker High or Low Mod...</td>\n", "      <td>[ADC Name, Antibody Name, Antigen Name, Payloa...</td>\n", "      <td>4</td>\n", "      <td>8</td>\n", "      <td>W4402167769</td>\n", "      <td>22.119</td>\n", "      <td>article</td>\n", "      <td>W4402167769</td>\n", "      <td>&lt;article dtd-version=\"1.3\" article-type=\"resea...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>105</th>\n", "      <td>W2800788936</td>\n", "      <td>[ADC EC₅₀, Anti-tumor activity Dose, Tumor gro...</td>\n", "      <td>[ADC Name, Antigen Name, Linker Name, Linker T...</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>W2800788936</td>\n", "      <td>21.023</td>\n", "      <td>article</td>\n", "      <td>W2800788936</td>\n", "      <td>&lt;article article-type=\"research-article\" xmlns...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>W1998210183</td>\n", "      <td>[ADC Internalization, BM pos cell decrease]</td>\n", "      <td>[Antibody Name, Antigen Name, Model Name, Mode...</td>\n", "      <td>2</td>\n", "      <td>5</td>\n", "      <td>W1998210183</td>\n", "      <td>20.745</td>\n", "      <td>article</td>\n", "      <td>W1998210183</td>\n", "      <td>&lt;article article-type=\"research-article\" xmlns...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>W2326085690</td>\n", "      <td>[]</td>\n", "      <td>[Antibody Name, SS Conjugation, SS Conjugation...</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>W2326085690</td>\n", "      <td>20.424</td>\n", "      <td>article</td>\n", "      <td>W2326085690</td>\n", "      <td>&lt;article xml:lang=\"EN\" article-type=\"research-...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>644</th>\n", "      <td>W4388422409</td>\n", "      <td>[Antigen Expression (Biomarker High or Low Mod...</td>\n", "      <td>[ADC Name, Antigen Name, Linker Name, Linker T...</td>\n", "      <td>6</td>\n", "      <td>10</td>\n", "      <td>W4388422409</td>\n", "      <td>19.977</td>\n", "      <td>article</td>\n", "      <td>W4388422409</td>\n", "      <td>&lt;article dtd-version=\"1.3\" article-type=\"resea...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>194</th>\n", "      <td>W2999664171</td>\n", "      <td>[Antigen Expression (Biomarker High or Low Mod...</td>\n", "      <td>[ADC Name, Antigen Name, Payload Name, Payload...</td>\n", "      <td>4</td>\n", "      <td>9</td>\n", "      <td>W2999664171</td>\n", "      <td>3.230</td>\n", "      <td>article</td>\n", "      <td>W2999664171</td>\n", "      <td>&lt;article article-type=\"research-article\" xmlns...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>502</th>\n", "      <td>W4225380120</td>\n", "      <td>[Antigen Expression (Biomarker High or Low Mod...</td>\n", "      <td>[ADC Name, ADC Company, Antibody Name, Antigen...</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>W4225380120</td>\n", "      <td>3.198</td>\n", "      <td>article</td>\n", "      <td>W4225380120</td>\n", "      <td>&lt;article article-type=\"research-article\" xml:l...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>624</th>\n", "      <td>W4386494902</td>\n", "      <td>[Reduced Red Blood Count, Decreased Body Weight]</td>\n", "      <td>[ADC Name, Antibody Name, Linker Name, Drug to...</td>\n", "      <td>2</td>\n", "      <td>6</td>\n", "      <td>W4386494902</td>\n", "      <td>3.153</td>\n", "      <td>article</td>\n", "      <td>W4386494902</td>\n", "      <td>&lt;article article-type=\"research-article\" xml:l...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>W1491690127</td>\n", "      <td>[Antigen Expression (Biomarker High or Low Mod...</td>\n", "      <td>[Linker Type, Linker Name, Payload Name, SS Co...</td>\n", "      <td>5</td>\n", "      <td>10</td>\n", "      <td>W1491690127</td>\n", "      <td>3.117</td>\n", "      <td>article</td>\n", "      <td>W1491690127</td>\n", "      <td>&lt;article article-type=\"research-article\" xmlns...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>W1969331750</td>\n", "      <td>[ADC Internalization]</td>\n", "      <td>[Antibody Name, Antigen Name, Model Name, Mode...</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>W1969331750</td>\n", "      <td>3.102</td>\n", "      <td>article</td>\n", "      <td>W1969331750</td>\n", "      <td>&lt;article article-type=\"research-article\" xmlns...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>150 rows × 10 columns</p>\n", "</div>"], "text/plain": ["        paper_id                            ans_endpoints_available  \\\n", "742  W4402167769  [Antigen Expression (Biomarker High or Low Mod...   \n", "105  W2800788936  [ADC EC₅₀, Anti-tumor activity Dose, Tumor gro...   \n", "14   W1998210183        [ADC Internalization, BM pos cell decrease]   \n", "42   W2326085690                                                 []   \n", "644  W4388422409  [Antigen Expression (Biomarker High or Low Mod...   \n", "..           ...                                                ...   \n", "194  W2999664171  [Antigen Expression (Biomarker High or Low Mod...   \n", "502  W4225380120  [Antigen Expression (Biomarker High or Low Mod...   \n", "624  W4386494902   [Reduced Red Blood Count, Decreased Body Weight]   \n", "0    W1491690127  [Antigen Expression (Biomarker High or Low Mod...   \n", "9    W1969331750                              [ADC Internalization]   \n", "\n", "                                ans_metadata_available  ans_endpoints_count  \\\n", "742  [ADC Name, Antibody Name, Antigen Name, Payloa...                    4   \n", "105  [ADC Name, Antigen Name, Linker Name, Linker T...                    6   \n", "14   [Antibody Name, Antigen Name, Model Name, Mode...                    2   \n", "42   [Antibody Name, SS Conjugation, SS Conjugation...                    0   \n", "644  [ADC Name, Antigen Name, Linker Name, Linker T...                    6   \n", "..                                                 ...                  ...   \n", "194  [ADC Name, Antigen Name, Payload Name, Payload...                    4   \n", "502  [ADC Name, ADC Company, Antibody Name, Antigen...                    6   \n", "624  [ADC Name, Antibody Name, Linker Name, Drug to...                    2   \n", "0    [Linker Type, Linker Name, Payload Name, SS Co...                    5   \n", "9    [Antibody Name, Antigen Name, Model Name, Mode...                    1   \n", "\n", "     ans_metadata_count oa_works_id_x    fwci     type oa_works_id_y  \\\n", "742                   8   W4402167769  22.119  article   W4402167769   \n", "105                   9   W2800788936  21.023  article   W2800788936   \n", "14                    5   W1998210183  20.745  article   W1998210183   \n", "42                    5   W2326085690  20.424  article   W2326085690   \n", "644                  10   W4388422409  19.977  article   W4388422409   \n", "..                  ...           ...     ...      ...           ...   \n", "194                   9   W2999664171   3.230  article   W2999664171   \n", "502                   9   W4225380120   3.198  article   W4225380120   \n", "624                   6   W4386494902   3.153  article   W4386494902   \n", "0                    10   W1491690127   3.117  article   W1491690127   \n", "9                     5   W1969331750   3.102  article   W1969331750   \n", "\n", "                                            rawContent  \n", "742  <article dtd-version=\"1.3\" article-type=\"resea...  \n", "105  <article article-type=\"research-article\" xmlns...  \n", "14   <article article-type=\"research-article\" xmlns...  \n", "42   <article xml:lang=\"EN\" article-type=\"research-...  \n", "644  <article dtd-version=\"1.3\" article-type=\"resea...  \n", "..                                                 ...  \n", "194  <article article-type=\"research-article\" xmlns...  \n", "502  <article article-type=\"research-article\" xml:l...  \n", "624  <article article-type=\"research-article\" xml:l...  \n", "0    <article article-type=\"research-article\" xmlns...  \n", "9    <article article-type=\"research-article\" xmlns...  \n", "\n", "[150 rows x 10 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["articles_sorted"]}, {"cell_type": "code", "execution_count": 9, "id": "f1ceb2c6", "metadata": {}, "outputs": [], "source": ["top_60_by_ep = articles_sorted.sort_values('ans_endpoints_count', ascending=False).head(60)\n", " "]}, {"cell_type": "code", "execution_count": 12, "id": "6c624609", "metadata": {}, "outputs": [], "source": ["top_60_by_ep.to_csv('top_60_by_ep.csv', index=False)"]}, {"cell_type": "code", "execution_count": 13, "id": "3a87d2ec", "metadata": {}, "outputs": [], "source": ["df2 = pd.read_parquet('/home/<USER>/top_reviews.parquet')"]}, {"cell_type": "code", "execution_count": 21, "id": "c76a549f", "metadata": {}, "outputs": [], "source": ["research_ids = top_60_by_ep[\"paper_id\"]\n", "research_ids.to_csv(\"top_60_by_ep_ids.csv\", index = False)"]}, {"cell_type": "code", "execution_count": 14, "id": "a57b0c33", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>paper_id</th>\n", "      <th>ans_endpoints_available</th>\n", "      <th>ans_metadata_available</th>\n", "      <th>ans_endpoints_count</th>\n", "      <th>ans_metadata_count</th>\n", "      <th>oa_works_id_x</th>\n", "      <th>fwci</th>\n", "      <th>type</th>\n", "      <th>oa_works_id_y</th>\n", "      <th>rawContent</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>W1983386052</td>\n", "      <td>[ADC IC₅₀, Anti-tumor activity Dose, Tumor gro...</td>\n", "      <td>[ADC Name, ADC Company, Antibody Name, Antigen...</td>\n", "      <td>4</td>\n", "      <td>13</td>\n", "      <td>W1983386052</td>\n", "      <td>3.647</td>\n", "      <td>review</td>\n", "      <td>W1983386052</td>\n", "      <td>&lt;article article-type=\"review-article\" xmlns:m...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>W2019434845</td>\n", "      <td>[ADC IC₅₀, ADC EC₅₀, Payload IC₅₀, Payload EC₅...</td>\n", "      <td>[Antigen Name, Model Type, Cancer Type, Model ...</td>\n", "      <td>10</td>\n", "      <td>4</td>\n", "      <td>W2019434845</td>\n", "      <td>0.636</td>\n", "      <td>review</td>\n", "      <td>W2019434845</td>\n", "      <td>&lt;article article-type=\"review-article\" xmlns:m...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>W2033619005</td>\n", "      <td>[Antigen Expression (Biomarker High or Low Mod...</td>\n", "      <td>[ADC Name, ADC Company, Antibody Name, Antigen...</td>\n", "      <td>8</td>\n", "      <td>13</td>\n", "      <td>W2033619005</td>\n", "      <td>0.370</td>\n", "      <td>review</td>\n", "      <td>W2033619005</td>\n", "      <td>&lt;article article-type=\"review-article\" xmlns:x...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>W2064424529</td>\n", "      <td>[DAR Time to 50%, Payload release, PK Dose, AD...</td>\n", "      <td>[ADC Name, Antibody Name, Antigen Name, Linker...</td>\n", "      <td>15</td>\n", "      <td>14</td>\n", "      <td>W2064424529</td>\n", "      <td>1.016</td>\n", "      <td>review</td>\n", "      <td>W2064424529</td>\n", "      <td>&lt;article article-type=\"review-article\" xmlns:x...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>W2122328108</td>\n", "      <td>[Tumor growth inhibition, Objective Response R...</td>\n", "      <td>[ADC Name, Antigen Name, Payload Name, Payload...</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>W2122328108</td>\n", "      <td>0.635</td>\n", "      <td>review</td>\n", "      <td>W2122328108</td>\n", "      <td>&lt;article article-type=\"review-article\" xmlns:m...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>101</th>\n", "      <td>W4402827995</td>\n", "      <td>[Antigen Expression (Biomarker High or Low Mod...</td>\n", "      <td>[Antigen Name, Model Type, Cancer Type, Model ...</td>\n", "      <td>5</td>\n", "      <td>4</td>\n", "      <td>W4402827995</td>\n", "      <td>0.440</td>\n", "      <td>review</td>\n", "      <td>W4402827995</td>\n", "      <td>&lt;article article-type=\"review-article\" xml:lan...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>102</th>\n", "      <td>W4404115496</td>\n", "      <td>[Emax, cytotoxicity, drug sensitivity score (D...</td>\n", "      <td>[Cancer Type, Model Type, Model Name, Drug Name]</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>W4404115496</td>\n", "      <td>0.000</td>\n", "      <td>review</td>\n", "      <td>W4404115496</td>\n", "      <td>&lt;article dtd-version=\"1.3\" article-type=\"revie...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>103</th>\n", "      <td>W4405519123</td>\n", "      <td>[Anti-Drug Antibodies, Decreased Body Weight, ...</td>\n", "      <td>[Antibody Name, Antigen Name, Payload Name, Mo...</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>W4405519123</td>\n", "      <td>0.000</td>\n", "      <td>review</td>\n", "      <td>W4405519123</td>\n", "      <td>&lt;article dtd-version=\"1.3\" article-type=\"revie...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>104</th>\n", "      <td>W4406229499</td>\n", "      <td>[Tumor growth inhibition, Anti-tumor activity ...</td>\n", "      <td>[Antigen Name, Linker Name, Conjugation Amino ...</td>\n", "      <td>2</td>\n", "      <td>6</td>\n", "      <td>W4406229499</td>\n", "      <td>0.000</td>\n", "      <td>review</td>\n", "      <td>W4406229499</td>\n", "      <td>&lt;article article-type=\"review-article\" xml:lan...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>105</th>\n", "      <td>W4406661909</td>\n", "      <td>[Tumor growth inhibition, Objective Response R...</td>\n", "      <td>[ADC Name, Antibody Name, Antigen Name, Payloa...</td>\n", "      <td>5</td>\n", "      <td>7</td>\n", "      <td>W4406661909</td>\n", "      <td>6.465</td>\n", "      <td>review</td>\n", "      <td>W4406661909</td>\n", "      <td>&lt;article dtd-version=\"1.3\" article-type=\"revie...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>90 rows × 10 columns</p>\n", "</div>"], "text/plain": ["        paper_id                            ans_endpoints_available  \\\n", "0    W1983386052  [ADC IC₅₀, Anti-tumor activity Dose, Tumor gro...   \n", "1    W2019434845  [ADC IC₅₀, ADC EC₅₀, Payload IC₅₀, Payload EC₅...   \n", "2    W2033619005  [Antigen Expression (Biomarker High or Low Mod...   \n", "3    W2064424529  [DAR Time to 50%, Payload release, PK Dose, AD...   \n", "4    W2122328108  [Tumor growth inhibition, Objective Response R...   \n", "..           ...                                                ...   \n", "101  W4402827995  [Antigen Expression (Biomarker High or Low Mod...   \n", "102  W4404115496  [Emax, cytotoxicity, drug sensitivity score (D...   \n", "103  W4405519123  [Anti-Drug Antibodies, Decreased Body Weight, ...   \n", "104  W4406229499  [Tumor growth inhibition, Anti-tumor activity ...   \n", "105  W4406661909  [Tumor growth inhibition, Objective Response R...   \n", "\n", "                                ans_metadata_available  ans_endpoints_count  \\\n", "0    [ADC Name, ADC Company, Antibody Name, Antigen...                    4   \n", "1    [Antigen Name, Model Type, Cancer Type, Model ...                   10   \n", "2    [ADC Name, ADC Company, Antibody Name, Antigen...                    8   \n", "3    [ADC Name, Antibody Name, Antigen Name, Linker...                   15   \n", "4    [ADC Name, Antigen Name, Payload Name, Payload...                    2   \n", "..                                                 ...                  ...   \n", "101  [Antigen Name, Model Type, Cancer Type, Model ...                    5   \n", "102   [Cancer Type, Model Type, Model Name, Drug Name]                    4   \n", "103  [Antibody Name, Antigen Name, Payload Name, Mo...                    5   \n", "104  [Antigen Name, Linker Name, Conjugation Amino ...                    2   \n", "105  [ADC Name, Antibody Name, Antigen Name, Payloa...                    5   \n", "\n", "     ans_metadata_count oa_works_id_x   fwci    type oa_works_id_y  \\\n", "0                    13   W1983386052  3.647  review   W1983386052   \n", "1                     4   W2019434845  0.636  review   W2019434845   \n", "2                    13   W2033619005  0.370  review   W2033619005   \n", "3                    14   W2064424529  1.016  review   W2064424529   \n", "4                     7   W2122328108  0.635  review   W2122328108   \n", "..                  ...           ...    ...     ...           ...   \n", "101                   4   W4402827995  0.440  review   W4402827995   \n", "102                   4   W4404115496  0.000  review   W4404115496   \n", "103                   5   W4405519123  0.000  review   W4405519123   \n", "104                   6   W4406229499  0.000  review   W4406229499   \n", "105                   7   W4406661909  6.465  review   W4406661909   \n", "\n", "                                            rawContent  \n", "0    <article article-type=\"review-article\" xmlns:m...  \n", "1    <article article-type=\"review-article\" xmlns:m...  \n", "2    <article article-type=\"review-article\" xmlns:x...  \n", "3    <article article-type=\"review-article\" xmlns:x...  \n", "4    <article article-type=\"review-article\" xmlns:m...  \n", "..                                                 ...  \n", "101  <article article-type=\"review-article\" xml:lan...  \n", "102  <article dtd-version=\"1.3\" article-type=\"revie...  \n", "103  <article dtd-version=\"1.3\" article-type=\"revie...  \n", "104  <article article-type=\"review-article\" xml:lan...  \n", "105  <article dtd-version=\"1.3\" article-type=\"revie...  \n", "\n", "[90 rows x 10 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["df2"]}, {"cell_type": "code", "execution_count": 15, "id": "e758c4bd", "metadata": {}, "outputs": [], "source": ["reviews_sorted = df2.sort_values('fwci', ascending=False)"]}, {"cell_type": "code", "execution_count": 16, "id": "63515ee1", "metadata": {}, "outputs": [], "source": ["top_40_reviews = reviews_sorted.head(40)"]}, {"cell_type": "code", "execution_count": 17, "id": "799c2912", "metadata": {}, "outputs": [], "source": ["top_40_reviews.to_csv('top_40_reviews.csv', index=False)"]}, {"cell_type": "code", "execution_count": 20, "id": "0a2173fb", "metadata": {}, "outputs": [], "source": ["review_ids = top_40_reviews[\"paper_id\"]\n", "review_ids.to_csv(\"top_40_review_ids.csv\", index = False)"]}], "metadata": {"kernelspec": {"display_name": "adc", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}