import pandas as pd
import sqlite3
import os
import numpy as np

# annotated_file_path = "data/adc_preclinical_review_annotated.xlsx"
annotated_file_path = ""
if os.path.exists(annotated_file_path):
    annotated_df = pd.read_excel(annotated_file_path)
else:
    print("No Annotated file exists")

# Connect to the SQLite database
db_path = "db/publication_profiles.db"
conn = sqlite3.connect(db_path)

# Fetch and print the names of all tables in the database
cursor = conn.cursor()
cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
tables = cursor.fetchall()

print("Tables in the database:")
for table in tables:
    print("\t-"+table[0])
conn.close()

# Process BIKG data
bikg_data = pd.read_parquet("./output.parquet")
bikg_data.reset_index(drop=True, inplace=True)

def get_full_text_flag(doc_sections):
    if doc_sections is None:
        return False
    # full_text = ""
    # body_subsection_count = 0
    for record in doc_sections:
        # if record.get("section") == "body":
        #     full_text += record.get("text") + " "
        #     body_subsection_count += 1
        if record.get("section") == "body" and isinstance(record.get("text"), str):
            return True
        
    # if len(full_text.split(" ")) > 150:
    #     return True
    return False

def get_full_text_words(doc_sections):
    if doc_sections is None:
        return 0
    full_text_count = 0
    for record in doc_sections:
        if record.get("section") == "body":
            full_text_count += len(record.get("text").split(" "))
    
    return full_text_count

def check_abstract_exists(row):
    is_available = False    
    if 'abstract' in row and row['abstract'] is not None:
        is_available = True
        return is_available
    if(isinstance(row['doc_sections'], np.ndarray)):
        for section in row['doc_sections']:
            if section['section'] == 'abstract':
                is_available = True
                return is_available
    return is_available

import json
bikg_data['is_genai_inference'] = bikg_data['is_genai_inference'].astype(bool)
bikg_data["has_partial_full_text_in_bikg"] = bikg_data['doc_sections'].apply(get_full_text_flag)
bikg_data["num_full_text_words"] = bikg_data['doc_sections'].apply(get_full_text_words)
bikg_data['open_access'] = bikg_data['open_access'].apply(lambda x: json.loads(x))
bikg_data['primary_location'] = bikg_data['primary_location'].apply(lambda x: json.loads(x))
bikg_data['is_open_accessible'] = bikg_data['open_access'].apply(lambda x: x.get("is_oa"))
bikg_data['has_full_text_in_some_repository'] = bikg_data['open_access'].apply(lambda x: x.get("any_repository_has_fulltext"))
bikg_data['is_abstract_available'] = bikg_data.apply(lambda row: check_abstract_exists(row.to_dict()), axis=1)
bikg_data = bikg_data.loc[bikg_data.groupby('id')['num_full_text_words'].idxmax()]


print("Number of rows in bikg_data:", len(bikg_data))
print("Value counts of has_partial_full_text_in_bikg:", bikg_data.value_counts("has_partial_full_text_in_bikg").to_dict())
print("Value counts of is_genai_inference:", bikg_data.value_counts("is_genai_inference").to_dict())
print("Value counts of is_open_accessible:", bikg_data.value_counts("is_open_accessible").to_dict())
print("Value counts of has_full_text_in_some_repository:", bikg_data.value_counts("has_full_text_in_some_repository").to_dict())
print("Value counts of is_abstract_available:", bikg_data.value_counts("is_abstract_available").to_dict())
print("Value counts of type:", bikg_data.value_counts("type").to_dict())
bikg_data.info()

# Read tables into DataFrames
conn = sqlite3.connect(db_path)

publication_analysis_df = pd.read_sql_query("SELECT * FROM fact_publication_analysis", conn)
if os.path.exists(annotated_file_path):
    publication_analysis_df = pd.merge(publication_analysis_df, annotated_df[['id', 'annotation']], left_on='id', right_on='id', how='left')
cancer_indications_df = pd.read_sql_query("SELECT * FROM fact_cancer_indications", conn)
research_objectives_df = pd.read_sql_query("SELECT * FROM fact_research_objectives", conn)

drop_columns = ["status", "message", "created_at"]
publication_analysis_df = publication_analysis_df.drop(columns=drop_columns, errors='ignore')
cancer_indications_df = cancer_indications_df.drop(columns=drop_columns, errors='ignore')
research_objectives_df = research_objectives_df.drop(columns=drop_columns, errors='ignore')

from datetime import datetime
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

# Create a Pandas Excel writer using XlsxWriter as the engine
excel_path = f"data/publication_profiler_summary_{timestamp}.xlsx"
with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
    publication_analysis_df.to_excel(writer, sheet_name='Publication Analysis', index=False)
    cancer_indications_df.to_excel(writer, sheet_name='Cancer Indications', index=False)
    research_objectives_df.to_excel(writer, sheet_name='Research Objectives', index=False)

def convert_list_to_enumerated_string(strings:pd.Series) -> str:
    strings = strings.tolist()
    return "\n".join([f"{i+1}. {s}" for i, s in enumerate(strings)])

unified_path = f"data/publication_profiler_unified_{timestamp}.xlsx"


with pd.ExcelWriter(unified_path, engine='xlsxwriter') as writer:
    agg_cancer_indications_df = cancer_indications_df.groupby('id').agg(convert_list_to_enumerated_string).reset_index()
    agg_research_objectives_df = research_objectives_df.groupby('id').agg(convert_list_to_enumerated_string).reset_index()
    unified_df = pd.merge(publication_analysis_df, agg_cancer_indications_df, left_on='id', right_on='id', how='left')
    unified_df = pd.merge(unified_df, agg_research_objectives_df, left_on='id', right_on='id', how='left')
    unified_df = pd.merge(bikg_data[['id', 'type', 'publication_year', 'is_open_accessible', 'has_full_text_in_some_repository', 'is_genai_inference', 'is_abstract_available', 'has_partial_full_text_in_bikg', 'doc_sections', 'doc_provenance', 'primary_location', 'oa_works_doi']], unified_df, left_on='id', right_on='id', how='left')
    # unified_df.to_excel(writer, sheet_name='FunnelData', index=False)

# Close the database connection
conn.close()

print("Number of rows in unified_df:", len(unified_df))
unified_df['is_genai_inference'] = unified_df['is_genai_inference'].astype(bool)
unified_df['has_partial_full_text_in_bikg'] = unified_df['has_partial_full_text_in_bikg'].astype(bool)
unified_df['adc_focus_final_assessment'] = unified_df['adc_focus_final_assessment'].astype(bool)
unified_df['preclinical_final_assessment'] = unified_df['preclinical_final_assessment'].astype(bool)
print("\n\n")

unified_df.info()

openalex_urls = [
    "https://openalex.org/W3126400332",
    "https://openalex.org/W4385978315",
    "https://openalex.org/W4395049238",
    "https://openalex.org/W4393229268",
    "https://openalex.org/W4389882757",
    "https://openalex.org/W3119692971",
    "https://openalex.org/W2784139266",
    "https://openalex.org/W3169380838",
    "https://openalex.org/W2977895894",
    "https://openalex.org/W3123971507",
    "https://openalex.org/W3110630842",
    "https://openalex.org/W2886552214",
    "https://openalex.org/W2911746982",
    "https://openalex.org/W3139514272",
    "https://openalex.org/W4372336332",
    "https://openalex.org/W4284897854",
    "https://openalex.org/W2746254865",
    "https://openalex.org/W4380272548",
    "https://openalex.org/W4280631979",
    "https://openalex.org/W4387230410",
    "https://openalex.org/W4401586017",
    "https://openalex.org/W3090364682",
    "https://openalex.org/W2969941297",
    "https://openalex.org/W3093537091",
    "https://openalex.org/W4281623024",
    "https://openalex.org/W2119870604",
    "https://openalex.org/W4210421364",
    "https://openalex.org/W2778226119",
    "https://openalex.org/W4400124600",
    "https://openalex.org/W4386125532",
    "https://openalex.org/W3001098362",
    "https://openalex.org/W4307230178",
    "https://openalex.org/W4381839168",
    "https://openalex.org/W4376958886",
    "https://openalex.org/W2099808918",
    "https://openalex.org/W4226097966",
    "https://openalex.org/W4390743855",
    "https://openalex.org/W4226170358",
    "https://openalex.org/W2314772071",
    "https://openalex.org/W4312117329",
]

bikg_data[bikg_data['id'] == 'https://openalex.org/W4312117329']

bikg_data[bikg_data['id'].isin(openalex_urls)][['id', 'oa_works_doi']].shape

# funnel data

adc_phrase_search = pd.Series(len(unified_df['id']) * [True])

is_article_or_review = unified_df['type'].isin(['article', 'review'])

is_genai_inference = (unified_df['is_genai_inference'] == True)

with_abstract = (unified_df['is_abstract_available'] == True)

has_bikg_full_text = (unified_df['has_partial_full_text_in_bikg'] == True)

is_adc_preclinical_focused = \
(
    # ADC = 1 and Preclinical = 1
    ((unified_df['adc_focus_final_assessment'] == True) & (unified_df['preclinical_final_assessment'] == True)) |
    # ADC = 0(low confidence) and Preclinical = 1
    ((unified_df['adc_focus_final_assessment'] == False) & (unified_df['adc_focus_confidence']=='Low') & (unified_df['preclinical_final_assessment'] == True)) |
    # ADC = 0(low confidence) and Preclinical = 0(low confidence)
    ((unified_df['adc_focus_final_assessment'] == False) & (unified_df['adc_focus_confidence']=='Low') & (unified_df['preclinical_final_assessment'] == False) & (unified_df['preclinical_confidence']=='Low') ) |
    # ADC = 1 and Preclinical = 0 and type = review
    ((unified_df['adc_focus_final_assessment'] == True) & (unified_df['preclinical_final_assessment'] == False) & (unified_df['type']=='review') )
)

p1 = adc_phrase_search
p2 = adc_phrase_search & is_article_or_review
p3 = adc_phrase_search & is_article_or_review & is_genai_inference
p4 = adc_phrase_search & is_article_or_review & is_genai_inference & with_abstract
p5 = adc_phrase_search & is_article_or_review & is_genai_inference & with_abstract & has_bikg_full_text
p6 = adc_phrase_search & is_article_or_review & is_genai_inference & with_abstract & has_bikg_full_text & is_adc_preclinical_focused

p7 = adc_phrase_search & is_article_or_review & ~is_genai_inference
p8 = adc_phrase_search & is_article_or_review & ~is_genai_inference & with_abstract
p9 = adc_phrase_search & is_article_or_review & ~is_genai_inference & with_abstract & has_bikg_full_text
p10 = adc_phrase_search & is_article_or_review & ~is_genai_inference & with_abstract & has_bikg_full_text & is_adc_preclinical_focused

print("ADC Phrase Search:", 
    (p1).sum()
)
print("Only Review/ Article papers:", 
    (p2).sum()
)
print("Is GenAI Inference:", 
    (p3).sum()
)
print("With Abstract:", 
    (p4).sum()
)
print("Has partial/full text in BIKG database:", 
    (p5).sum()
)
print("ADC Preclinical Focused:",
    (p6).sum()  
)

unified_df.columns

unified_df['has_full_text_in_some_repository'].value_counts()

# Fix: extract last part of URL for rows where p6 is True
unified_df['oa_works_id'] = unified_df['id'].str.split('/').str[-1]

required_df = unified_df[p6]
required_df['doc_provenance'].value_counts()

required_ids = unified_df[p6]['oa_works_id']

ids = pd.read_parquet('/home/<USER>/xml_ids.parquet')
ids['oa_works_id']

# Check how many required_ids are in ids['oa_works_id']
num_in_ids = required_ids.isin(ids['oa_works_id']).sum()
print(f"Number of required_ids present in ids['oa_works_id']: {num_in_ids} / {len(required_ids)}")

springer_df = unified_df[p6 & (unified_df['doc_provenance'] == 'springer')]
wiley_df = unified_df[p6 & (unified_df['doc_provenance'] == 'wiley')]
# springer_df[springer_df['oa_works_doi'] == '10.1038/s41375-024-02510-5']['type']
wiley_df['type'].value_counts()


article_ids = unified_df.loc[p6 & (unified_df['type'] == 'article'), 'oa_works_id'].reset_index(drop=True)
review_ids = unified_df.loc[p6 & (unified_df['type'] == 'review'), 'oa_works_id'].reset_index(drop=True)

# Save article_ids and review_ids to one Excel file, each as a separate sheet
ids_excel_path = f"/home/<USER>/scale_up_ids.xlsx"
with pd.ExcelWriter(ids_excel_path, engine='xlsxwriter') as writer:
    article_ids.to_frame(name='oa_works_id').to_excel(writer, sheet_name='article_ids', index=False)
    review_ids.to_frame(name='oa_works_id').to_excel(writer, sheet_name='review_ids', index=False)
print(f"Saved article_ids and review_ids to {ids_excel_path}")

wiley_np = ["10.1002/ijc.28950","10.1002/pbc.24647","10.1002/bdd.1953","10.1002/pros.22916","10.1111/nyas.12499","10.1111/j.1476-5381.2012.02138.x","10.1002/ijc.28555","10.1111/cbdd.12085","10.1111/bcp.12044","10.1111/cas.12062","10.1016/j.febslet.2013.11.030","10.1111/j.1349-7006.2011.01954.x","10.1002/phar.1170","10.1002/ijc.23910","10.1002/path.2545","10.1002/cncr.24790","10.1002/pros.21164","10.1111/ejh.12408","10.1111/bjh.13205","10.1111/ejh.12360","10.1002/biot.201200250","10.1111/j.1365-2141.2004.05322.x","10.1634/theoncologist.2008-0100","10.1016/j.molonc.2008.02.002","10.1002/anie.201408103","10.1002/anie.201507391","10.1002/pbc.25688","10.1111/bjh.12788","10.3322/canjclin.56.4.226","10.1002/pros.23124","10.1002/cncr.29895","10.1002/chir.20990","10.1002/rcm.7511","10.1111/bjh.14145","10.1002/cmdc.201600372","10.1002/ijc.30569","10.1111/joim.12582","10.1111/bjh.14524","10.1111/bjh.14770","10.1002/hon.2438_121","10.1111/cei.13017","10.1002/ijc.30870","10.1002/jcph.981","10.1002/ijc.31124","10.1002/bmc.4229","10.1002/chem.201800859","10.1002/cmdc.201700722","10.1002/jat.3582","10.1002/ajh.25071","10.1002/chem.201803174","10.1002/iub.1916","10.1002/anie.201807619","10.1002/cncr.31657","10.1002/hep.30326","10.1002/cmdc.201800598","10.1002/jcp.27419","10.1002/jcp.27085","10.1111/bjh.15783","10.1002/ijc.32154","10.1002/ijc.32273","10.1002/anie.201814215","10.1002/jcp.28453","10.1002/cmdc.201900077","10.1002/cbic.201900178","10.1002/pbc.27765","10.1002/jcp.28490","10.1002/ijc.32408","10.1002/hon.134_2630","10.1002/hon.90_2629","10.1002/jcp.28967","10.1111/febs.14967","10.1002/chem.201903127","10.1002/bmc.4855","10.1002/jlb.5mr0620-063rr","10.1111/bjh.17235","10.1002/hon.154_2880","10.1111/bjd.20770","10.1002/ijc.33865","10.1002/ange.201408103","10.1002/ange.201601506","10.1002/ange.201507391","10.1002/ange.201814215","10.1002/ange.201904193","10.1002/ange.201608292","10.1002/ajh.26750","10.1111/bph.15988","10.1002/hon.3164_413","10.1002/cncr.34904","10.1002/bdd.2371","10.1002/cpt.3102","10.1002/ajh.27165","10.1111/bjh.19658"]
springer_np = ["10.1038/s41375-024-02510-5"]
wiley_np_ids = wiley_df[wiley_df['oa_works_doi'].isin(wiley_np)]['oa_works_id'].reset_index(drop=True)
springer_np_ids = springer_df[springer_df['oa_works_doi'].isin(springer_np)]['oa_works_id'].reset_index(drop=True)
# Save wiley_ids and springer_ids to one Excel file, each as a separate sheet
ids_excel_path = f"/home/<USER>/missing_studies.xlsx"
with pd.ExcelWriter(ids_excel_path, engine='xlsxwriter') as writer:
    wiley_np_ids.to_frame(name='oa_works_id').to_excel(writer, sheet_name='wiley studies', index=False)
    springer_np_ids.to_frame(name='oa_works_id').to_excel(writer, sheet_name='springer studies', index=False)
print(f"Saved wiley_ids and springer_ids to {ids_excel_path}")

europepmc_articles = unified_df[p6 & (unified_df['oa_works_id'].isin(ids['oa_works_id'])) & (unified_df['type'] == 'article')][['oa_works_id', 'doc_sections']]
europepmc_reviews = unified_df[p6 & (unified_df['oa_works_id'].isin(ids['oa_works_id'])) & (unified_df['type'] == 'review')][['oa_works_id', 'doc_sections']]

unified_df[p4 & is_adc_preclinical_focused & (unified_df['id'] == 'https://openalex.org/W2314772071')]['doc_provenance'].iloc[0]

unified_df[p4 & is_adc_preclinical_focused & ~p6]['doc_provenance'].value_counts()

unified_df[p4 & is_adc_preclinical_focused & unified_df['id'].isin(openalex_urls)].shape

import asyncio
import json
from dataclasses import dataclass
from openai import AsyncAzureOpenAI

@dataclass
class ans_type:
    endpoints_available: list[str]
    metadata_available: list[str]
    endpoints_count: int
    metadata_count: int

@dataclass
class result:
    paper_id: str
    ans: ans_type

client = AsyncAzureOpenAI(
    azure_endpoint='https://az-preview-eu-openai-poc-swd16.openai.azure.com/',
    azure_deployment='gpt-4.1',
    api_key='********************************',
    api_version='2024-12-01-preview',
)

async def get_response(study, endpoints, metadata):
    messages = [
        {"role": "system", "content": "You are a helpful bioinformatician who knows everything about oncology research."},
        {"role": "user", "content": f"Given the study (research paper) and list of endpoints, I want you to list all the endpoints and metadata from the list that are present in the paper and also give the total number. Answer in this json format: {ans_type.__annotations__}. Respond in JSON.\
         Here is the research paper study: \n\n {study} \n\n Now here is the list of endpoints we seek: \n\n {endpoints} \n\n Here is the metadata we seek: \n\n {metadata}" }
    ]

    while True:
        response = await client.chat.completions.create(
            model="gpt-4.1",
            messages=messages,
            response_format={"type": "json_object"},
            temperature=0.0,
            seed=777
        )
        content = response.choices[0].message.content
        try:
            data = json.loads(content)
            ans = ans_type(**data)
            print(ans)
            return ans
        except Exception as e:
            # Add the assistant's response and the error as context for the next round
            messages.append({'role': 'assistant', 'content': content})
            messages.append({'role': 'user', 'content': f"ERROR: {e}. Please answer in the exact JSON format: {ans_type.__annotations__}"})

# Usage in a notebook cell:
ans = await get_response("", "", "")

endpoints = '''Antigen Expression (Biomarker High or Low Model): Antigen expression refers to the measurable level of a target antigen present on or within biological samples such as tumor cell lines, primary cells, or tissue specimens. These measurable levels are subsequently categorized qualitatively into high or low antigen expression based on predefined thresholds.

Specific Antigen Expression (H-Score): H-score is a semi-quantitative scoring system used to evaluate antigen expression in tissue specimens through immunohistochemistry (IHC) and cell lines through immunocytochemistry (ICC). It combines the intensity of staining and the percentage of positive cells to provide a comprehensive score ranging from 0 to 300.

ADC EC₅₀: The concentration of the intact ADC molecule required to achieve 50% of its maximum biological effect. The units of concentration are nM, pM, µM, ng/mL, µg/mL, etc.

Payload EC₅₀: The concentration of the free (unconjugated) cytotoxic drug required to achieve 50% of its maximum biological effect. The units of concentration are nM, pM, µM, ng/mL, µg/mL, etc.

ADC Kd: The dissociation constant (Kd) is a biophysical parameter that quantifies the intrinsic binding affinity between the antibody component of an ADC and its target antigen. It represents the concentration of the ADC at which 50% of the antigen binding sites are occupied under equilibrium conditions. The units of Kd are M, mM, µM, nM, pM, etc.

ADC Internalization: ADC internalization refers to the process by which an antibody-drug conjugate (ADC), after binding to its target antigen on the cell surface, is taken up into the cell via endocytosis. This metric quantifies the proportion of the total administered ADC that is successfully internalized by target cells within a specified time frame. It is calculated by comparing the amount of ADC internalized to the total amount initially available for binding and uptake, and is usually expressed as a percentage (%) at a given time point.

ADC treatment concentration: The ADC treatment concentration refers to the defined amount of ADC applied to a co-culture system of biomarker-positive (BM+) and biomarker-negative (BM−) cells to evaluate the bystander killing effect. It is usually expressed in molar units such as nM or pM.

BM pos cell decrease: Biomarker-positive (BM+) cell decrease refers to the reduction in the number or viability of cells that express the target antigen following treatment with an ADC in a co-culture system. This decrease is primarily due to direct ADC-mediated cytotoxicity, and is typically quantified using percentage decrease relative to untreated control, absolute cell count reduction, fold change in viable cell number, or fluorescence/luminescence units from viability assays.

BM neg cell decrease: Biomarker-negative (BM−) cell decrease refers to the reduction in the number or viability of cells that do not express the target antigen following treatment with an ADC in a co-culture system. This decrease is attributed to the bystander killing effect, where the cytotoxic payload—released from neighboring biomarker-positive cells—diffuses into and kills adjacent antigen-negative cells. It is typically quantified using similar metrics as for BM+ cells.

ADC IC₅₀: The concentration of the intact antibody-drug conjugate required to inhibit 50% of a specific biological function, typically cell proliferation or viability, in a target cell population. The units of concentration are nM, pM, µM, ng/mL, µg/mL, etc.

Payload IC₅₀: The concentration of the free (unconjugated) cytotoxic drug required to inhibit 50% of cell proliferation or viability, independent of the antibody or targeting mechanism. The units of concentration are nM, pM, µM, ng/mL, µg/mL, etc.

ADC GI₅₀: The concentration of an ADC required to inhibit 50% of net cell growth compared to untreated controls over a defined time period. It accounts for both cytostatic and cytotoxic effects, and provides a comprehensive measure of the ADC’s impact on cell population dynamics. The units are nM, pM, µM, ng/mL, µg/mL, etc.

Anti-tumor activity Dose: Dose of ADC administered per kg of body weight in animal studies. It is typically expressed as mg/kg or mpk.

Tumor growth inhibition: The measure of the effectiveness of a treatment in inhibiting tumor growth. TGI is typically expressed as a percentage, indicating the reduction in tumor size or growth rate compared to a control group.

Objective Response Rate (ORR): Objective Response Rate (ORR) in CDX or PDX models refers to the proportion of tumor-bearing animals that exhibit a measurable reduction in tumor volume following treatment with an investigational agent, such as an ADC. ORR is expressed as a percentage (%) and classified according to predefined criteria into complete or partial response.

DAR Time to 50%: The time required for the Drug-to-Antibody Ratio (DAR) of an ADC to decrease by 50% in circulation. It indicates linker stability and drug release kinetics, and is usually expressed as hours or days.

Payload release: Percentage of ADC payload released into circulation or tumor site at a particular time point.

PK Dose: The administered dose of an ADC in preclinical models (e.g., rodents, NHPs) to evaluate its absorption, distribution, metabolism, and excretion (ADME).

ADC Cmax: The peak plasma concentration of the full ADC (antibody + payload) after administration. It is usually expressed as µg/mL or ng/mL.

ADC AUC: Represents the total systemic exposure of the ADC (antibody + payload) over time. Indicates ADC stability and clearance, and is usually expressed as µgh/mL, mgh/L, or ng*h/mL.

ADC t 1/2: The time required for the plasma concentration of the full ADC (antibody + payload) to decrease by 50%. Reflects ADC stability and clearance rate, and is usually expressed as hours or days.

TAb Cmax: The peak plasma concentration of total antibody (TAb), including both conjugated (ADC) and unconjugated antibody forms. It is usually expressed as µg/mL.

TAb AUC: Measures the total exposure of the total antibody (both conjugated and unconjugated forms). Helps evaluate antibody persistence and deconjugation, and is usually expressed as µgh/mL, mgh/L, or ng*h/mL.

TAb t 1/2: The time required for the total antibody (conjugated + unconjugated) to decrease by 50%. Indicates antibody persistence in circulation, and is usually expressed as hours or days.

Payload Cmax: The peak plasma concentration of the released cytotoxic drug (payload) in circulation. It is usually expressed as µg/mL or ng/mL.

Payload AUC: Measures the total systemic exposure of the released cytotoxic payload over time. Indicates payload clearance and potential toxicity risk, and is usually expressed as ng*h/mL.

Payload t 1/2: The time required for the plasma concentration of the released cytotoxic payload to decrease by 50%. Indicates payload clearance and systemic exposure risk, and is usually expressed as hours or days.

Albumin reconjugation: Albumin reconjugation refers to the binding or reattachment of a drug or payload molecule from ADC to serum albumin, the most abundant protein in human plasma.

Anti-Drug Antibodies: ADAs (Anti-Drug Antibodies) are antibodies produced by the immune system in response to a therapeutic protein or biologic drug, such as an Antibody-Drug Conjugate (ADC).

Toxicology Dose: Drug dose administered per kg of body weight in animal studies.

Toxicity Frequency: Dosing frequency of ADC to minimize toxicity.

Lethal Dose: The dose of an ADC that causes death in preclinical animal models, used to assess toxicity limits. It helps determine the margin of safety for human dosing and is expressed in mg/kg.

HNSTD: The maximum dose of a drug or treatment (in this case, an ADC) that can be administered without causing severe or life-threatening toxic effects.

Decreased Food Consumption: Effect on food intake.

GI Issues: Gastrointestinal toxicity observed.

Decreased Body Weight: Weight loss due to ADC administration.

Reduced Red Blood Count: Decrease in circulating red blood cells, indicating potential anemia or bone marrow suppression due to ADC toxicity.

Reduced Hemoglobin: Lower hemoglobin levels, suggesting reduced oxygen-carrying capacity, often associated with anemia.

Reticulocyte: Measures the percentage or absolute count of immature red blood cells (reticulocytes) in circulation, indicating bone marrow activity and response to ADC-induced anemia.

Reduced Albumin: Decrease in serum albumin levels, which may indicate liver toxicity, protein loss, or systemic inflammation due to ADC treatment.

White Blood Cells: Measures the total count of circulating white blood cells, which are essential for the immune response.

Reduced Lymphocytes: Decrease in lymphocyte count, indicating potential immunosuppression or bone marrow toxicity.

Reduced Neutrophils: Decrease in neutrophil count, which may increase infection risk and indicate myelosuppression due to ADC treatment.

Increased AST: AST is a liver enzyme, and elevated levels indicate potential liver toxicity or hepatocellular damage due to ADC administration.

Increased ALT: ALT is another liver enzyme, and its elevation suggests liver injury, inflammation, or drug-induced hepatotoxicity.

Lung Inflammation: Inflammatory response in lung tissue due to ADC-related toxicity, leading to damage, fibrosis, or impaired function.

Interstitial Lung Disease: ILD refers to lung inflammation and fibrosis caused by ADC-related toxicity. It is a critical safety concern in preclinical and clinical ADC studies.

Target Organ Issues: Identifies organ-specific toxicities caused by ADCs, which may result from target antigen expression in normal tissues or off-target effects of the cytotoxic payload.

'''

metadata = '''ADC Name: Name of the Antibody-Drug Conjugate (ADC).

ADC Company: Biotech or pharmaceutical company developing the ADC.

Antibody Name: Monoclonal antibody (mAb) used in the ADC.

Antibody Binding Epitope: A binding epitope is the specific region on an antigen that is recognized and bound by an antibody. It is the molecular address where the antibody docks to initiate an immune response or therapeutic effect. It is usually represented as a short peptide sequence of amino acids.

Antibody Binding Epitope Location: Location of the binding epitope relative to the cell membrane. Membrane-proximal epitopes enhance internalization.

ADC Species Cross Reactivity: ADC species cross-reactivity refers to the ability of the ADC to bind to the same or similar antigen in different animal species (e.g., mouse, rat, monkey, dog) as it does in humans.

Antigen Name: Tumor-associated antigen recognized by the ADC.

Linker Name: Chemical linker attaching the payload to the antibody.

Linker Type: Indicates if the linker is cleavable (pH-sensitive, enzyme-sensitive, or redox-sensitive).

Payload Name: Cytotoxic drug attached to the ADC.

Payload Target: Mode of action of the payload (e.g., tubulin inhibitor, DNA-damaging agent).

SS Conjugation: Indicates whether the ADC is conjugated at a specific site on the antibody.

SS Conjugation Technology: Technology used for site-specific conjugation (e.g., engineered amino acids, enzymatic methods).

Conjugation Amino Acid: Amino acid on the antibody used for drug or payload attachment (Cysteine, Lysine, Glutamine, Glycan, etc.).

Conjugation Sites: Specific locations on the antibody where the drug is conjugated.

Drug to Antibody Ratio: Average number of payload molecules conjugated per antibody.

Clinical Trial Phase: Current clinical trial phase of the ADC.

Clinical Data Availability: Indicates whether clinical trial data is available.

Model Name: Name of the experimental model.

Model Type: A specific biological system or organism used to simulate human physiological or pathological conditions for the purpose of studying the pharmacological, toxicological, or mechanistic effects of therapeutic intervention.

Cancer Type: Broad classification of cancer based on the tissue or cell of origin for a given experimental model.

GLP or Non-GLP: Determines whether the study follows Good Laboratory Practice (GLP) regulations or is Non-GLP exploratory research.'''

# europepmc_articles = 
articles_data = []
i = 1
for row in europepmc_articles.itertuples(index=False):
    print(f"Running for {i}/{europepmc_articles.shape[0]} "+"="*10)
    ans = await get_response(row.doc_sections, endpoints, metadata)
    res = result(paper_id=row.oa_works_id, ans=ans)
    articles_data.append(res)
    i = i+1
    
reviews_data = []
i = 1
for row in europepmc_reviews.itertuples(index=False):
    print(f"Running for {i}/{europepmc_reviews.shape[0]} "+"="*10)
    ans = await get_response(row.doc_sections, endpoints, metadata)
    res = result(paper_id=row.oa_works_id, ans=ans)
    reviews_data.append(res)
    i = i+1

# Save articles_data and reviews_data as two Excel sheets, flattening nested dicts

import pandas as pd
from pandas import json_normalize

def flatten_and_save(data_list, filename, sheet_name):
    # Convert dataclass objects to dicts if needed
    dict_list = []
    for item in data_list:
        if hasattr(item, '__dict__'):
            d = item.__dict__.copy()
            # If 'ans' is a dataclass, flatten it too
            if 'ans' in d and hasattr(d['ans'], '__dict__'):
                for k, v in d['ans'].__dict__.items():
                    d[f'ans_{k}'] = v
                del d['ans']
            dict_list.append(d)
        else:
            dict_list.append(item)
    df = json_normalize(dict_list)
    return df

articles_df = flatten_and_save(articles_data, "articles.xlsx", "articles")
reviews_df = flatten_and_save(reviews_data, "reviews.xlsx", "reviews")

excel_path = "/home/<USER>/paper_richness.xlsx"
with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
    articles_df.to_excel(writer, sheet_name='articles', index=False)
    reviews_df.to_excel(writer, sheet_name='reviews', index=False)

print(f"Saved flattened articles and reviews to {excel_path}")

openalex_db_path = "./db/openalex_data.db"
conn = sqlite3.connect(openalex_db_path)
dim_openalex_works_df = pd.read_sql_query("SELECT * FROM dim_openalex_works", conn)
conn.close()

# Now dim_openalex_works_df contains the table data
unified_df = pd.merge(unified_df, dim_openalex_works_df[['id', 'fwci']], on='id', how='left')

df = pd.read_parquet("/home/<USER>/zs_oa_adc_curated_annotated_with_content_europepmc_raw_xml_2025.03-05.parquet")
# df[df['oa_works_id'].isin(openalex_urls.split('/')[-1])]

df[df['oa_works_id'] == 'W4390743855']['rawContent'].iloc[0]

# Extract OpenAlex IDs from URLs
openalex_ids = [url.split('/')[-1] for url in openalex_urls]
df['oa_works_id']

required_df = unified_df[p6]

# required_df[['adc_focus_final_assessment', 'preclinical_final_assessment', 'adc_focus_confidence', 'preclinical_confidence']]
required_ids = unified_df[p5 & ((unified_df['adc_focus_final_assessment'] == True) & (unified_df['preclinical_final_assessment'] == True))]['oa_works_id']

# ((unified_df['adc_focus_final_assessment'] == False) & (unified_df['adc_focus_confidence']=='Low') & (unified_df['preclinical_final_assessment'] == False) & (unified_df['preclinical_confidence']=='Low') ) |

# List of target oa_works_id values
target_ids = [
    "W4284897854",
    "W2314772071",
    "W4226170358",
    "W4400124600",
    "W4226097966",
    "W4292181683",
    "W2064424529",
    "W4393115738"
]

filtered_df = df[df['oa_works_id'].isin(target_ids)][['oa_works_id', 'rawContent']]
filtered_df.to_parquet('/home/<USER>/dry_run_xmls.parquet', index=False)

# Filter required_df for these IDs
# filtered_rows = required_df[required_df['oa_works_id']=="W2314772071"]
# filtered_rows['doc_provenance']

required_ids[required_ids.isin(target_ids)]

# required_ids[required_ids.isin(target_ids)]
all_ids = unified_df[p5 & ((unified_df['adc_focus_final_assessment'] == True) & (unified_df['preclinical_final_assessment'] == True)) & (unified_df['doc_provenance'] == 'europepmc') & ~unified_df['oa_works_id'].isin(target_ids)]['oa_works_id']

articles_df = articles_df.merge(
    unified_df[['oa_works_id', 'fwci', 'type']],
    left_on='paper_id',
    right_on='oa_works_id',
    how='left'
)

reviews_df = reviews_df.merge(
    unified_df[['oa_works_id', 'fwci', 'type']],
    left_on='paper_id',
    right_on='oa_works_id',
    how='left'
)

articles_df = articles_df.merge(
    df[['oa_works_id', 'rawContent']],
    left_on='paper_id',
    right_on='oa_works_id',
    how='left'
)

reviews_df = reviews_df.merge(
    df[['oa_works_id', 'rawContent']],
    left_on='paper_id',
    right_on='oa_works_id',
    how='left'
)

# Remove rows from articles_df and reviews_df where paper_id appears more than once

# Find duplicated paper_ids in articles_df
duplicate_article_ids = articles_df['paper_id'][articles_df['paper_id'].duplicated(keep=False)].unique()
articles_df = articles_df[~articles_df['paper_id'].isin(duplicate_article_ids)]

# Find duplicated paper_ids in reviews_df
duplicate_review_ids = reviews_df['paper_id'][reviews_df['paper_id'].duplicated(keep=False)].unique()
reviews_df = reviews_df[~reviews_df['paper_id'].isin(duplicate_review_ids)]

bad_reviews = unified_df[p6 &  ((unified_df['adc_focus_final_assessment'] == True) & (unified_df['preclinical_final_assessment'] == False) & (unified_df['type']=='review') )]
bad_reviews = bad_reviews.merge(
    df[['oa_works_id', 'rawContent']],
    left_on='oa_works_id',
    right_on='oa_works_id',
    how='left'
)
# bad_reviews.info()

# Find duplicated paper_ids in reviews_df
duplicate_bad_review_ids = bad_reviews['oa_works_id'][bad_reviews['oa_works_id'].duplicated(keep=False)].unique()
bad_reviews = bad_reviews[~bad_reviews['oa_works_id'].isin(duplicate_bad_review_ids)]
bad_reviews = bad_reviews[~bad_reviews['rawContent'].isna()]
bad_reviews.to_parquet('/home/<USER>/bad_reviews.parquet')