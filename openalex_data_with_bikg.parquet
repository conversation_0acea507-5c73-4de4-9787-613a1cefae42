{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["current working directory: /projects/ods/us_eds/users/ai_hub/suraj_workspace/adc_data_center\n"]}], "source": ["import os\n", "os.chdir(\"../\")\n", "print(\"current working directory:\", os.getcwd())"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["No Annotated file exists\n", "Tables in the database:\n", "\t-fact_publication_analysis\n", "\t-fact_cancer_indications\n", "\t-fact_research_objectives\n"]}], "source": ["import pandas as pd\n", "import sqlite3\n", "\n", "# annotated_file_path = \"data/adc_preclinical_review_annotated.xlsx\"\n", "annotated_file_path = \"\"\n", "if os.path.exists(annotated_file_path):\n", "    annotated_df = pd.read_excel(annotated_file_path)\n", "else:\n", "    print(\"No Annotated file exists\")\n", "\n", "# Connect to the SQLite database\n", "db_path = \"db/publication_profiles.db\"\n", "conn = sqlite3.connect(db_path)\n", "\n", "# Fetch and print the names of all tables in the database\n", "cursor = conn.cursor()\n", "cursor.execute(\"SELECT name FROM sqlite_master WHERE type='table';\")\n", "tables = cursor.fetchall()\n", "\n", "print(\"Tables in the database:\")\n", "for table in tables:\n", "    print(\"\\t-\"+table[0])\n", "conn.close()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of rows in bikg_data: 22176\n", "Value counts of has_partial_full_text_in_bikg: {False: 15127, True: 7049}\n", "Value counts of is_genai_inference: {True: 12032, False: 10144}\n", "Value counts of is_open_accessible: {True: 13815, False: 8361}\n", "Value counts of has_full_text_in_some_repository: {False: 11561, True: 10615}\n", "Value counts of is_abstract_available: {True: 17000, False: 5176}\n", "Value counts of type: {'article': 14795, 'review': 4869, 'preprint': 1450, 'book-chapter': 288, 'letter': 173, 'dissertation': 132, 'editorial': 132, 'other': 108, 'supplementary-materials': 82, 'paratext': 59, 'book': 29, 'dataset': 27, 'peer-review': 26, 'erratum': 4, 'report': 1, 'reference-entry': 1}\n", "<class 'pandas.core.frame.DataFrame'>\n", "Index: 22176 entries, 4746 to 20103\n", "Data columns (total 34 columns):\n", " #   Column                            Non-Null Count  Dtype  \n", "---  ------                            --------------  -----  \n", " 0   id                                22176 non-null  string \n", " 1   title                             22147 non-null  string \n", " 2   abstract                          17000 non-null  string \n", " 3   ids                               22176 non-null  string \n", " 4   indexed_in                        22176 non-null  string \n", " 5   authorships                       22176 non-null  string \n", " 6   cited_by_count                    22176 non-null  int64  \n", " 7   concepts                          22176 non-null  string \n", " 8   fwci                              19890 non-null  float64\n", " 9   is_paratext                       22176 non-null  int64  \n", " 10  is_retracted                      22176 non-null  int64  \n", " 11  keywords                          22176 non-null  string \n", " 12  language                          22109 non-null  string \n", " 13  license                           22176 non-null  string \n", " 14  locations                         22176 non-null  string \n", " 15  open_access                       22176 non-null  object \n", " 16  primary_location                  22176 non-null  string \n", " 17  publication_date                  22176 non-null  string \n", " 18  publication_year                  22176 non-null  int64  \n", " 19  topics                            22176 non-null  string \n", " 20  type                              22176 non-null  string \n", " 21  type_crossref                     22176 non-null  string \n", " 22  created_at                        22176 non-null  string \n", " 23  doc_hash                          12032 non-null  object \n", " 24  is_genai_inference                22176 non-null  bool   \n", " 25  is_genai_train                    12032 non-null  object \n", " 26  doc_sections                      9987 non-null   object \n", " 27  content_size                      22176 non-null  bool   \n", " 28  body_flag                         22176 non-null  bool   \n", " 29  has_partial_full_text_in_bikg     22176 non-null  bool   \n", " 30  num_full_text_words               22176 non-null  int64  \n", " 31  is_open_accessible                22176 non-null  bool   \n", " 32  has_full_text_in_some_repository  22176 non-null  bool   \n", " 33  is_abstract_available             22176 non-null  bool   \n", "dtypes: bool(7), float64(1), int64(5), object(4), string(17)\n", "memory usage: 4.9+ MB\n"]}], "source": ["# Process BIKG data\n", "bikg_data = pd.read_parquet(\"db/openalex_data_with_bikg.parquet\")\n", "bikg_data.reset_index(drop=True, inplace=True)\n", "\n", "def get_full_text_flag(doc_sections):\n", "    if doc_sections is None:\n", "        return False\n", "    # full_text = \"\"\n", "    # body_subsection_count = 0\n", "    for record in doc_sections:\n", "        # if record.get(\"section\") == \"body\":\n", "        #     full_text += record.get(\"text\") + \" \"\n", "        #     body_subsection_count += 1\n", "        if record.get(\"section\") == \"body\" and isinstance(record.get(\"text\"), str):\n", "            return True\n", "        \n", "    # if len(full_text.split(\" \")) > 150:\n", "    #     return True\n", "    return False\n", "\n", "def get_full_text_words(doc_sections):\n", "    if doc_sections is None:\n", "        return 0\n", "    full_text_count = 0\n", "    for record in doc_sections:\n", "        if record.get(\"section\") == \"body\":\n", "            full_text_count += len(record.get(\"text\").split(\" \"))\n", "    \n", "    return full_text_count\n", "\n", "def check_abstract_exists(row):\n", "    is_available = False    \n", "    if row['abstract'] is not None:\n", "        is_available = True\n", "        return is_available\n", "    if isinstance(row['doc_sections'], list):\n", "        for section in row['doc_sections']:\n", "            if section['section'] == 'abstract':\n", "                is_available = True\n", "                return is_available\n", "    return is_available\n", "\n", "import json\n", "bikg_data['is_genai_inference'] = bikg_data['is_genai_inference'].astype(bool)\n", "bikg_data[\"has_partial_full_text_in_bikg\"] = bikg_data['doc_sections'].apply(get_full_text_flag)\n", "bikg_data[\"num_full_text_words\"] = bikg_data['doc_sections'].apply(get_full_text_words)\n", "bikg_data['open_access'] = bikg_data['open_access'].apply(lambda x: json.loads(x))\n", "bikg_data['is_open_accessible'] = bikg_data['open_access'].apply(lambda x: x.get(\"is_oa\"))\n", "bikg_data['has_full_text_in_some_repository'] = bikg_data['open_access'].apply(lambda x: x.get(\"any_repository_has_fulltext\"))\n", "bikg_data['is_abstract_available'] = bikg_data.apply(lambda row: check_abstract_exists(row.to_dict()), axis=1)\n", "bikg_data = bikg_data.loc[bikg_data.groupby('id')['content_size'].idxmax()]\n", "\n", "\n", "print(\"Number of rows in bikg_data:\", len(bikg_data))\n", "print(\"Value counts of has_partial_full_text_in_bikg:\", bikg_data.value_counts(\"has_partial_full_text_in_bikg\").to_dict())\n", "print(\"Value counts of is_genai_inference:\", bikg_data.value_counts(\"is_genai_inference\").to_dict())\n", "print(\"Value counts of is_open_accessible:\", bikg_data.value_counts(\"is_open_accessible\").to_dict())\n", "print(\"Value counts of has_full_text_in_some_repository:\", bikg_data.value_counts(\"has_full_text_in_some_repository\").to_dict())\n", "print(\"Value counts of is_abstract_available:\", bikg_data.value_counts(\"is_abstract_available\").to_dict())\n", "print(\"Value counts of type:\", bikg_data.value_counts(\"type\").to_dict())\n", "bikg_data.info()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# Read tables into DataFrames\n", "conn = sqlite3.connect(db_path)\n", "\n", "publication_analysis_df = pd.read_sql_query(\"SELECT * FROM fact_publication_analysis\", conn)\n", "if os.path.exists(annotated_file_path):\n", "    publication_analysis_df = pd.merge(publication_analysis_df, annotated_df[['id', 'annotation']], left_on='id', right_on='id', how='left')\n", "cancer_indications_df = pd.read_sql_query(\"SELECT * FROM fact_cancer_indications\", conn)\n", "research_objectives_df = pd.read_sql_query(\"SELECT * FROM fact_research_objectives\", conn)\n", "\n", "drop_columns = [\"status\", \"message\", \"created_at\"]\n", "publication_analysis_df = publication_analysis_df.drop(columns=drop_columns, errors='ignore')\n", "cancer_indications_df = cancer_indications_df.drop(columns=drop_columns, errors='ignore')\n", "research_objectives_df = research_objectives_df.drop(columns=drop_columns, errors='ignore')\n", "\n", "from datetime import datetime\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "# Create a Pandas Excel writer using XlsxWriter as the engine\n", "excel_path = f\"data/publication_profiler_summary_{timestamp}.xlsx\"\n", "with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:\n", "    publication_analysis_df.to_excel(writer, sheet_name='Publication Analysis', index=False)\n", "    cancer_indications_df.to_excel(writer, sheet_name='Cancer Indications', index=False)\n", "    research_objectives_df.to_excel(writer, sheet_name='Research Objectives', index=False)\n", "\n", "def convert_list_to_enumerated_string(strings:pd.Series) -> str:\n", "    strings = strings.tolist()\n", "    return \"\\n\".join([f\"{i+1}. {s}\" for i, s in enumerate(strings)])\n", "\n", "unified_path = f\"data/publication_profiler_unified_{timestamp}.xlsx\"\n", "with pd.ExcelWriter(unified_path, engine='xlsxwriter') as writer:\n", "    agg_cancer_indications_df = cancer_indications_df.groupby('id').agg(convert_list_to_enumerated_string).reset_index()\n", "    agg_research_objectives_df = research_objectives_df.groupby('id').agg(convert_list_to_enumerated_string).reset_index()\n", "    unified_df = pd.merge(publication_analysis_df, agg_cancer_indications_df, left_on='id', right_on='id', how='left')\n", "    unified_df = pd.merge(unified_df, agg_research_objectives_df, left_on='id', right_on='id', how='left')\n", "    unified_df = pd.merge(bikg_data[['id', 'type', 'publication_year', 'is_open_accessible', 'has_full_text_in_some_repository', 'is_genai_inference', 'is_abstract_available', 'has_partial_full_text_in_bikg']], unified_df, left_on='id', right_on='id', how='left')\n", "    unified_df.to_excel(writer, sheet_name='FunnelData', index=False)\n", "\n", "# Close the database connection\n", "conn.close()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of rows in unified_df: 22176\n", "\n", "\n", "\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 22176 entries, 0 to 22175\n", "Data columns (total 24 columns):\n", " #   Column                            Non-Null Count  Dtype \n", "---  ------                            --------------  ----- \n", " 0   id                                22176 non-null  object\n", " 1   type                              22176 non-null  string\n", " 2   publication_year                  22176 non-null  int64 \n", " 3   is_open_accessible                22176 non-null  bool  \n", " 4   has_full_text_in_some_repository  22176 non-null  bool  \n", " 5   is_genai_inference                22176 non-null  bool  \n", " 6   is_abstract_available             22176 non-null  bool  \n", " 7   has_partial_full_text_in_bikg     22176 non-null  bool  \n", " 8   title                             16059 non-null  object\n", " 9   abstract                          16061 non-null  object\n", " 10  adc_focus_reasoning               16061 non-null  object\n", " 11  adc_focus_confidence              16061 non-null  object\n", " 12  adc_focus_final_assessment        22176 non-null  bool  \n", " 13  adc_names                         3405 non-null   object\n", " 14  preclinical_reasoning             16061 non-null  object\n", " 15  preclinical_confidence            16061 non-null  object\n", " 16  preclinical_final_assessment      22176 non-null  bool  \n", " 17  indication_reasoning              5726 non-null   object\n", " 18  indication_confidence             5726 non-null   object\n", " 19  cancer_type                       5726 non-null   object\n", " 20  cancer_subtype                    5726 non-null   object\n", " 21  study_focus_reasoning             6364 non-null   object\n", " 22  study_focus_confidence            6364 non-null   object\n", " 23  primary_focus                     6364 non-null   object\n", "dtypes: bool(7), int64(1), object(15), string(1)\n", "memory usage: 3.0+ MB\n"]}], "source": ["print(\"Number of rows in unified_df:\", len(unified_df))\n", "unified_df['is_genai_inference'] = unified_df['is_genai_inference'].astype(bool)\n", "unified_df['has_partial_full_text_in_bikg'] = unified_df['has_partial_full_text_in_bikg'].astype(bool)\n", "unified_df['adc_focus_final_assessment'] = unified_df['adc_focus_final_assessment'].astype(bool)\n", "unified_df['preclinical_final_assessment'] = unified_df['preclinical_final_assessment'].astype(bool)\n", "print(\"\\n\\n\")\n", "\n", "unified_df.info()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ADC Phrase Search: 22176\n", "Only Review/ Article papers: 19664\n", "Is GenAI Inference: 11645\n", "With Abstract: 9576\n", "Has partial/full text in BIKG database: 6378\n"]}], "source": ["# funnel data\n", "\n", "adc_phrase_search = pd.Series(len(unified_df['id'].unique()) * [True])\n", "\n", "is_article_or_review = unified_df['type'].isin(['article', 'review'])\n", "\n", "is_genai_inference = (unified_df['is_genai_inference'] == True)\n", "\n", "with_abstract = (unified_df['is_abstract_available'] == True)\n", "\n", "has_bikg_full_text = (unified_df['has_partial_full_text_in_bikg'] == True)\n", "\n", "print(\"ADC Phrase Search:\", \n", "    (adc_phrase_search).sum()\n", ")\n", "print(\"Only Review/ Article papers:\", \n", "    (adc_phrase_search & is_article_or_review).sum()\n", ")\n", "print(\"Is GenAI Inference:\", \n", "    (adc_phrase_search & is_article_or_review & is_genai_inference).sum()\n", ")\n", "print(\"With Abstract:\", \n", "    (adc_phrase_search & is_article_or_review & is_genai_inference & with_abstract).sum()\n", ")\n", "print(\"Has partial/full text in BIKG database:\", \n", "    (adc_phrase_search & is_article_or_review & is_genai_inference & with_abstract & has_bikg_full_text).sum()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# funnel data\n", "adc_phrase_search = pd.Series(len(unified_df['id'].unique()) * [True])\n", "\n", "is_article_or_review = unified_df['type'].isin(['article', 'review'])\n", "\n", "with_abstract = unified_df['abstract'].notna()\n", "\n", "\n", "is_adc_preclinical_focused = \\\n", "(\n", "    # ADC = 1 and Preclinical = 1\n", "    ((unified_df['adc_focus_final_assessment'] == True) & (unified_df['preclinical_final_assessment'] == True)) |\n", "    # ADC = 0(low confidence) and Preclinical = 1\n", "    ((unified_df['adc_focus_final_assessment'] == False) & (unified_df['adc_focus_confidence']=='Low') & (unified_df['preclinical_final_assessment'] == True)) |\n", "    # ADC = 0(low confidence) and Preclinical = 0(low confidence)\n", "    ((unified_df['adc_focus_final_assessment'] == False) & (unified_df['adc_focus_confidence']=='Low') & (unified_df['preclinical_final_assessment'] == False) & (unified_df['preclinical_confidence']=='Low') ) |\n", "    # ADC = 1 and Preclinical = 0 and type = review\n", "    ((unified_df['adc_focus_final_assessment'] == True) & (unified_df['preclinical_final_assessment'] == False) & (unified_df['type']=='review') )\n", ")\n", "\n", "\n", "is_genai_inference = (unified_df['is_genai_inference'] == True)\n", "\n", "has_bikg_data = (unified_df['has_partial_full_text_in_bikg'] == True)\n", "\n", "is_open_accessible = (unified_df['is_open_accessible'] == True)\n", "\n", "has_open_full_text = (is_open_accessible & (unified_df['has_full_text_in_some_repository'] == True))\n", "has_bikg_full_text = (is_open_accessible & (unified_df['has_partial_full_text_in_bikg'] == True))\n", "\n", "print(\"ADC Phrase Search:\", \n", "    (adc_phrase_search).sum()\n", ")\n", "print(\"Only Review/ Article papers:\", \n", "    (adc_phrase_search & is_article_or_review).sum()\n", ")\n", "print(\"With Abstract:\", \n", "    (adc_phrase_search & is_article_or_review & with_abstract).sum()\n", ")\n", "print(\"Is GenAI Inference:\", \n", "    (adc_phrase_search & is_article_or_review & with_abstract & is_genai_inference).sum()\n", ")\n", "print(\"Is ADC Preclinical Focused:\", \n", "    (adc_phrase_search & is_article_or_review & with_abstract & is_genai_inference & is_adc_preclinical_focused).sum()\n", ")\n", "print(\"Is Open Access:\", \n", "    (adc_phrase_search & is_article_or_review & with_abstract & is_genai_inference & is_adc_preclinical_focused & is_open_accessible).sum()\n", ")\n", "print(\"Has full text in some open repository:\", \n", "    (adc_phrase_search & is_article_or_review & with_abstract & is_genai_inference & is_adc_preclinical_focused & is_open_accessible & has_open_full_text).sum()\n", ")\n", "\n", "print(\"Has partial/full text in BIKG database:\", \n", "    (adc_phrase_search & is_article_or_review & with_abstract & is_genai_inference & is_adc_preclinical_focused & is_open_accessible & has_bikg_full_text).sum()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "adc", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}